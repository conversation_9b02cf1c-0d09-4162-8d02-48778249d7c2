#!/usr/bin/env node

import { XcodeServer } from "./server.js";
import { StringUtils } from "./utils/stringUtilities.js";

/**
 * Professional CLI interface for Xcode MCP Server
 */
class XcodeMCPCLI {
  private server: XcodeServer | null = null;
  private startTime: number = 0;

  /**
   * Display startup banner with version info
   */
  private displayBanner(): void {
    console.error("🚀 Xcode MCP Server v1.0.3");
  }

  /**
   * Display server status with project context
   */
  private displayServerStatus(server: XcodeServer): void {
    const uptime = StringUtils.formatDuration(Date.now() - this.startTime);
    console.error(`✅ Ready (${uptime}) - MCP protocol active`);

    if (server.activeProject) {
      console.error(`📁 Project: ${server.activeProject.name}`);
    } else {
      console.error("⚠️  No active project");
    }
  }

  /**
   * Initialize and start the server
   */
  async start(): Promise<void> {
    this.startTime = Date.now();

    try {
      this.displayBanner();
      this.server = new XcodeServer();
      await this.server.start();
      this.displayServerStatus(this.server);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error(`❌ Startup failed: ${message}`);
      console.error("💡 Try: DEBUG=true for details, check Xcode installation");
      process.exit(1);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    if (this.server) {
      console.error("🛑 Shutting down...");
      this.server.dispose();
      console.error("✅ Shutdown complete");
    }
  }
}

// Initialize and start the server
async function main() {
  const cli = new XcodeMCPCLI();

  // Handle graceful shutdown signals
  const shutdown = async () => {
    await cli.shutdown();
    process.exit(0);
  };

  process.on("SIGINT", shutdown);
  process.on("SIGTERM", shutdown);

  await cli.start();
}

// Start the server
main().catch((error) => {
  const message = error instanceof Error ? error.message : String(error);
  console.error(`❌ Fatal error: ${message}`);
  process.exit(1);
});
