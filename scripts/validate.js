#!/usr/bin/env node

/**
 * Final Validation Script for Xcode MCP Server
 */

import fs from "fs/promises";
import path from "path";

class FinalValidator {
  constructor() {
    this.results = [];
    this.projectRoot = process.cwd();
  }

  async runValidation() {
    console.log("🔍 Starting Final Validation of Xcode MCP Server\n");

    await this.validateBuildSystem();
    await this.validateFileStructure();
    await this.validateToolRegistration();
    await this.validateDocumentation();
    await this.validateProductionReadiness();

    this.generateFinalReport();
  }

  async validateBuildSystem() {
    console.log("📦 Validating Build System...");

    try {
      const distExists = await this.fileExists("dist");
      if (distExists) {
        const distFiles = await fs.readdir("dist");
        this.addResult(
          "Build",
          "Compilation",
          "pass",
          `Build successful - ${distFiles.length} files generated`
        );
      } else {
        this.addResult(
          "Build",
          "Compilation",
          "fail",
          "Dist directory not found"
        );
      }

      const indexExists = await this.fileExists("dist/index.js");
      this.addResult(
        "Build",
        "Entry Point",
        indexExists ? "pass" : "fail",
        indexExists ? "Main entry point exists" : "Main entry point missing"
      );

      if (indexExists) {
        const stats = await fs.stat("dist/index.js");
        const isExecutable = (stats.mode & parseInt("111", 8)) !== 0;
        this.addResult(
          "Build",
          "Executable",
          isExecutable ? "pass" : "warning",
          isExecutable
            ? "Entry point is executable"
            : "Entry point may not be executable"
        );
      }
    } catch (error) {
      this.addResult(
        "Build",
        "System Check",
        "fail",
        `Build validation failed: ${error.message}`
      );
    }
  }

  async validateFileStructure() {
    console.log("📁 Validating File Structure...");

    const expectedStructure = {
      "src/server.ts": "Main server implementation",
      "src/index.ts": "CLI entry point",
      "src/tools/categories.ts": "Tool categorization",
      "src/tools/core/": "Core tool implementations",
      "src/tools/project/": "Project management tools",
      "src/tools/cocoapods/": "CocoaPods tools",
      "src/tools/spm/": "Swift Package Manager tools",
      "src/tools/simulator/": "Simulator control tools",
      "src/tools/xcode/": "Xcode utility tools",
      "src/utils/": "Utility functions and infrastructure",
      "docs/COMPREHENSIVE_GUIDE.md": "Comprehensive documentation",
      "package.json": "Package configuration",
      "tsconfig.json": "TypeScript configuration",
    };

    for (const [filePath, description] of Object.entries(expectedStructure)) {
      const exists = await this.fileExists(filePath);
      this.addResult(
        "Structure",
        description,
        exists ? "pass" : "fail",
        exists ? `${filePath} exists` : `${filePath} missing`
      );
    }
  }

  async validateToolRegistration() {
    console.log("🔧 Validating Tool Registration...");

    const expectedCounts = {
      project: 14,
      file: 13,
      build: 7,
      "package-management": 22,
      simulator: 11,
      "xcode-utilities": 9,
      development: 4,
    };

    const totalExpected = Object.values(expectedCounts).reduce(
      (a, b) => a + b,
      0
    );

    this.addResult(
      "Tools",
      "Expected Count",
      "pass",
      `Expected ${totalExpected} tools across ${
        Object.keys(expectedCounts).length
      } categories`
    );

    const toolFiles = [
      "src/tools/project/index.ts",
      "src/tools/cocoapods/index.ts",
      "src/tools/spm/index.ts",
      "src/tools/simulator/index.ts",
      "src/tools/core/fileOperations.ts",
      "src/tools/core/buildSystem.ts",
      "src/tools/core/xcodeUtilities.ts",
      "src/tools/core/performanceDashboard.ts",
      "src/tools/core/productionReadinessDashboard.ts",
      "src/tools/core/toolValidationDashboard.ts",
      "src/tools/xcode/consolidatedTools.ts",
      "src/tools/xcode/distributionTools.ts",
    ];

    let toolFilesExist = 0;
    for (const toolFile of toolFiles) {
      const exists = await this.fileExists(toolFile);
      if (exists) toolFilesExist++;
    }

    this.addResult(
      "Tools",
      "Tool Files",
      toolFilesExist === toolFiles.length ? "pass" : "fail",
      `${toolFilesExist}/${toolFiles.length} tool files exist`
    );
  }

  async validateDocumentation() {
    console.log("📚 Validating Documentation...");

    const docFiles = [
      "README.md",
      "docs/COMPREHENSIVE_GUIDE.md",
      "docs/ARCHITECTURE.md",
      "docs/API.md",
    ];

    for (const docFile of docFiles) {
      const exists = await this.fileExists(docFile);
      if (exists) {
        try {
          const content = await fs.readFile(docFile, "utf-8");
          const wordCount = content.split(/\s+/).length;
          this.addResult(
            "Documentation",
            path.basename(docFile),
            "pass",
            `${docFile} exists (${wordCount} words)`
          );
        } catch (error) {
          this.addResult(
            "Documentation",
            path.basename(docFile),
            "warning",
            `${docFile} exists but couldn't read content`
          );
        }
      } else {
        this.addResult(
          "Documentation",
          path.basename(docFile),
          "fail",
          `${docFile} missing`
        );
      }
    }
  }

  async validateProductionReadiness() {
    console.log("🚀 Validating Production Readiness...");

    const productionFeatures = [
      {
        file: "src/utils/performanceMonitor.ts",
        feature: "Performance Monitoring",
      },
      { file: "src/utils/cacheManager.ts", feature: "Advanced Caching" },
      { file: "src/utils/securityManager.ts", feature: "Security Manager" },
      {
        file: "src/utils/commandExecutor.ts",
        feature: "Secure Command Execution",
      },
      {
        file: "src/utils/serviceContainer.ts",
        feature: "Dependency Injection",
      },
      { file: "src/utils/healthMonitor.ts", feature: "Health Monitoring" },
      { file: "src/utils/pathManager.ts", feature: "Path Validation" },
    ];

    for (const { file, feature } of productionFeatures) {
      const exists = await this.fileExists(file);
      this.addResult(
        "Production",
        feature,
        exists ? "pass" : "fail",
        exists ? `${feature} implemented` : `${feature} missing`
      );
    }

    try {
      const packageJson = JSON.parse(
        await fs.readFile("package.json", "utf-8")
      );
      const hasProductionBuild = packageJson.scripts?.["build:production"];
      const hasVerifyScript = packageJson.scripts?.["verify"];

      this.addResult(
        "Production",
        "Build Scripts",
        hasProductionBuild ? "pass" : "warning",
        hasProductionBuild
          ? "Production build script exists"
          : "Production build script missing"
      );

      this.addResult(
        "Production",
        "Verification",
        hasVerifyScript ? "pass" : "warning",
        hasVerifyScript
          ? "Verification script exists"
          : "Verification script missing"
      );
    } catch (error) {
      this.addResult(
        "Production",
        "Package Config",
        "fail",
        "Could not read package.json"
      );
    }
  }

  addResult(category, test, status, message, details) {
    this.results.push({ category, test, status, message, details });

    const icon = status === "pass" ? "✅" : status === "warning" ? "⚠️" : "❌";
    console.log(`  ${icon} ${test}: ${message}`);
  }

  async fileExists(filePath) {
    try {
      await fs.access(path.join(this.projectRoot, filePath));
      return true;
    } catch {
      return false;
    }
  }

  generateFinalReport() {
    const categories = [...new Set(this.results.map((r) => r.category))];
    const totalTests = this.results.length;
    const passed = this.results.filter((r) => r.status === "pass").length;
    const warnings = this.results.filter((r) => r.status === "warning").length;
    const failed = this.results.filter((r) => r.status === "fail").length;

    console.log("\n" + "═".repeat(80));
    console.log("🎯 FINAL VALIDATION REPORT");
    console.log("═".repeat(80));

    console.log(`\n📊 SUMMARY:`);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`⚠️  Warnings: ${warnings}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`Success Rate: ${Math.round((passed / totalTests) * 100)}%`);

    console.log(`\n📂 BY CATEGORY:`);
    for (const category of categories) {
      const categoryResults = this.results.filter(
        (r) => r.category === category
      );
      const categoryPassed = categoryResults.filter(
        (r) => r.status === "pass"
      ).length;
      const categoryTotal = categoryResults.length;
      console.log(`${category}: ${categoryPassed}/${categoryTotal} passed`);
    }

    if (failed > 0) {
      console.log(`\n❌ FAILURES:`);
      this.results
        .filter((r) => r.status === "fail")
        .forEach((result) => {
          console.log(
            `• ${result.category} - ${result.test}: ${result.message}`
          );
        });
    }

    if (warnings > 0) {
      console.log(`\n⚠️  WARNINGS:`);
      this.results
        .filter((r) => r.status === "warning")
        .forEach((result) => {
          console.log(
            `• ${result.category} - ${result.test}: ${result.message}`
          );
        });
    }

    console.log("\n🎉 PRODUCTION READINESS STATUS:");
    if (failed === 0 && warnings <= 3) {
      console.log("✅ PRODUCTION READY - All critical tests passed!");
      console.log("🚀 The Xcode MCP Server is ready for deployment.");
    } else if (failed === 0) {
      console.log("⚠️  MOSTLY READY - Minor issues to address");
      console.log("🔧 Address warnings before production deployment.");
    } else {
      console.log("❌ NOT READY - Critical issues found");
      console.log("🛠️  Fix failed tests before deployment.");
    }

    console.log("\n" + "═".repeat(80));
    console.log("📖 For detailed information, see docs/COMPREHENSIVE_GUIDE.md");
    console.log("🔧 For troubleshooting, enable DEBUG=true");
    console.log("═".repeat(80));

    process.exit(failed > 0 ? 1 : 0);
  }
}

async function main() {
  const validator = new FinalValidator();
  await validator.runValidation();
}

main().catch((error) => {
  console.error("❌ Validation failed:", error);
  process.exit(1);
});
